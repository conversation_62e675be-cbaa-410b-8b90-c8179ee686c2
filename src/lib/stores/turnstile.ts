import { writable, derived } from 'svelte/store';
import { browser } from '$app/environment';

// Turnstile verification state
interface TurnstileState {
  isLoaded: boolean;
  isVerified: boolean;
  currentToken: string | null;
  lastVerification: number | null;
  pendingVerifications: Set<string>;
}

// Initial state
const initialState: TurnstileState = {
  isLoaded: false,
  isVerified: false,
  currentToken: null,
  lastVerification: null,
  pendingVerifications: new Set()
};

// Create the main store
export const turnstileState = writable<TurnstileState>(initialState);

// Derived stores for convenient access
export const isVerified = derived(
  turnstileState,
  ($state) => $state.isVerified
);

export const currentToken = derived(
  turnstileState,
  ($state) => $state.currentToken
);

export const isScriptLoaded = derived(
  turnstileState,
  ($state) => $state.isLoaded
);

// Configuration store
interface TurnstileConfig {
  enabled: boolean;
  siteKey: string;
  verificationTimeout: number;
  retryAttempts: number;
}

const defaultConfig: TurnstileConfig = {
  enabled: true,
  siteKey: import.meta.env.PUBLIC_TURNSTILE_SITE_KEY || '',
  verificationTimeout: 5 * 60 * 1000, // 5 minutes
  retryAttempts: 3
};

export const turnstileConfig = writable<TurnstileConfig>(defaultConfig);

// Actions for managing Turnstile state
export const turnstileActions = {
  // Mark script as loaded
  setScriptLoaded: () => {
    turnstileState.update(state => ({
      ...state,
      isLoaded: true
    }));
  },

  // Set verification success
  setVerified: (token: string) => {
    turnstileState.update(state => ({
      ...state,
      isVerified: true,
      currentToken: token,
      lastVerification: Date.now()
    }));
  },

  // Clear verification
  clearVerification: () => {
    turnstileState.update(state => ({
      ...state,
      isVerified: false,
      currentToken: null,
      lastVerification: null
    }));
  },

  // Add pending verification
  addPendingVerification: (widgetId: string) => {
    turnstileState.update(state => ({
      ...state,
      pendingVerifications: new Set([...state.pendingVerifications, widgetId])
    }));
  },

  // Remove pending verification
  removePendingVerification: (widgetId: string) => {
    turnstileState.update(state => {
      const newPending = new Set(state.pendingVerifications);
      newPending.delete(widgetId);
      return {
        ...state,
        pendingVerifications: newPending
      };
    });
  },

  // Check if verification is still valid
  isVerificationValid: (): boolean => {
    let currentState: TurnstileState;
    let currentConfig: TurnstileConfig;

    // Get current state values
    const unsubscribeState = turnstileState.subscribe(s => currentState = s);
    const unsubscribeConfig = turnstileConfig.subscribe(c => currentConfig = c);

    // Clean up subscriptions immediately
    unsubscribeState();
    unsubscribeConfig();

    if (!currentState!.isVerified || !currentState!.lastVerification) {
      return false;
    }

    const now = Date.now();
    const elapsed = now - currentState!.lastVerification;

    return elapsed < currentConfig!.verificationTimeout;
  },

  // Reset all state
  reset: () => {
    turnstileState.set(initialState);
  }
};

// Client-side utilities
export const turnstileUtils = {
  // Verify a token on the server
  verifyToken: async (token: string, endpoint = '/api/verify-turnstile'): Promise<boolean> => {
    if (!browser) return false;

    try {
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token })
      });

      const result = await response.json();
      return result.success === true;
    } catch (error) {
      console.error('Turnstile verification error:', error);
      return false;
    }
  },

  // Get the current verification status
  getVerificationStatus: (): boolean => {
    return turnstileActions.isVerificationValid();
  },

  // Wait for script to load
  waitForScript: (): Promise<void> => {
    return new Promise((resolve) => {
      if (typeof window.turnstile !== 'undefined') {
        turnstileActions.setScriptLoaded();
        resolve();
        return;
      }

      const checkScript = () => {
        if (typeof window.turnstile !== 'undefined') {
          turnstileActions.setScriptLoaded();
          resolve();
        } else {
          setTimeout(checkScript, 100);
        }
      };

      checkScript();
    });
  },

  // Create a verification session
  createSession: async (token: string): Promise<{ success: boolean; sessionId?: string }> => {
    try {
      const response = await fetch('/api/turnstile/session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ token })
      });

      const result = await response.json();
      
      if (result.success) {
        turnstileActions.setVerified(token);
        return { success: true, sessionId: result.sessionId };
      }

      return { success: false };
    } catch (error) {
      console.error('Session creation error:', error);
      return { success: false };
    }
  }
};

// Initialize store in browser
if (browser) {
  // Check if script is already loaded
  if (typeof window.turnstile !== 'undefined') {
    turnstileActions.setScriptLoaded();
  }

  // Clean up expired verifications periodically
  setInterval(() => {
    if (!turnstileActions.isVerificationValid()) {
      turnstileActions.clearVerification();
    }
  }, 60 * 1000); // Check every minute
}
