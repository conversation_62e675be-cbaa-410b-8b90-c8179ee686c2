<script lang="ts">
import { onMount } from 'svelte';
import { browser } from '$app/environment';
import { goto } from '$app/navigation';
import { page } from '$app/stores';

/**
 * Minimal Enterprise Turnstile Verification
 * Clean, distraction-free security verification page
 */

let turnstileContainer = $state<HTMLDivElement>();
let widgetId: string | null = null;
let isLoaded = $state(false);
let isVerifying = $state(false);
let verificationComplete = $state(false);
let error = $state('');

const SITE_KEY = import.meta.env.PUBLIC_TURNSTILE_SITE_KEY;

// Load Turnstile script
const loadTurnstile = () => {
  return new Promise<void>((resolve, reject) => {
    if (typeof window.turnstile !== 'undefined') {
      resolve();
      return;
    }

    const existingScript = document.querySelector('script[src*="turnstile"]');
    if (existingScript) {
      const checkTurnstile = () => {
        if (typeof window.turnstile !== 'undefined') {
          resolve();
        } else {
          setTimeout(checkTurnstile, 100);
        }
      };
      checkTurnstile();
      return;
    }

    const script = document.createElement('script');
    script.src = 'https://challenges.cloudflare.com/turnstile/v0/api.js';
    script.async = true;
    script.defer = true;
    
    script.onload = () => {
      let attempts = 0;
      const checkTurnstile = () => {
        if (typeof window.turnstile !== 'undefined') {
          resolve();
        } else if (attempts < 50) {
          attempts++;
          setTimeout(checkTurnstile, 100);
        } else {
          reject(new Error('Turnstile API not available'));
        }
      };
      checkTurnstile();
    };
    
    script.onerror = () => reject(new Error('Failed to load Turnstile'));
    document.head.appendChild(script);
  });
};

// Handle successful verification
const handleVerificationSuccess = async (token: string) => {
  isVerifying = true;
  error = '';
  
  try {
    const response = await fetch('/api/auth/verify-clearance', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ token })
    });

    if (!response.ok) {
      throw new Error('Verification failed');
    }

    verificationComplete = true;
    
    // Redirect after brief delay
    const returnUrl = $page.url.searchParams.get('return') || '/auth/login';
    setTimeout(() => goto(returnUrl), 1000);

  } catch (err) {
    error = 'Verification failed. Please try again.';
    isVerifying = false;
    
    if (widgetId && window.turnstile) {
      window.turnstile.reset(widgetId);
    }
  }
};

// Handle verification errors
const handleVerificationError = () => {
  error = 'Verification failed. Please refresh and try again.';
  isVerifying = false;
};

// Render Turnstile widget
const renderTurnstile = async () => {
  if (!browser || !turnstileContainer || !SITE_KEY) {
    error = 'Configuration error';
    return;
  }
  
  try {
    await loadTurnstile();
    
    if (widgetId) {
      window.turnstile.remove(widgetId);
      widgetId = null;
    }

    widgetId = window.turnstile.render(turnstileContainer, {
      sitekey: SITE_KEY,
      theme: 'auto',
      size: 'normal',
      retry: 'auto',
      callback: handleVerificationSuccess,
      'error-callback': handleVerificationError,
      'expired-callback': () => {
        error = 'Verification expired. Please try again.';
        isVerifying = false;
      },
      'timeout-callback': () => {
        error = 'Verification timed out. Please try again.';
        isVerifying = false;
      }
    });
    
    isLoaded = true;
  } catch (err) {
    error = 'Failed to load verification. Please refresh the page.';
  }
};

onMount(() => {
  if (!SITE_KEY || SITE_KEY === 'undefined') {
    error = 'Configuration error. Please contact support.';
    return;
  }
  
  renderTurnstile();
  
  return () => {
    if (browser && widgetId && window.turnstile) {
      window.turnstile.remove(widgetId);
    }
  };
});
</script>

<!-- Minimal verification page -->
<div class="verify-container">
  {#if verificationComplete}
    <!-- Success state -->
    <div class="success-check">✓</div>
    
  {:else if isVerifying}
    <!-- Loading state -->
    <div class="loading-spinner"></div>
    
  {:else}
    <!-- Turnstile widget -->
    <div bind:this={turnstileContainer} class="turnstile-widget"></div>
  {/if}
  
  <!-- Minimal error display -->
  {#if error}
    <p class="error-text">{error}</p>
  {/if}
</div>

<style>
  .verify-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: white;
    padding: 20px;
  }

  .turnstile-widget {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 65px;
  }

  .success-check {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    background: #22c55e;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    font-weight: bold;
  }

  .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .error-text {
    margin-top: 16px;
    color: #dc2626;
    font-size: 14px;
    text-align: center;
    max-width: 300px;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* Responsive design */
  @media (max-width: 640px) {
    .verify-container {
      padding: 16px;
    }
  }
</style>
