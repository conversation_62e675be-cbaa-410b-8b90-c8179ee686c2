<script lang="ts">
  import { onMount } from 'svelte';
  import { session, isAuthenticated } from '$lib/stores/auth.js';
  import { nhost } from '$lib/stores/nhost.js';

  let debugInfo = $state({
    localStorage: {} as Record<string, string>,
    sessionStorage: {} as Record<string, string>,
    cookies: '',
    indexedDbDatabases: [] as (IDBDatabaseInfo | string)[],
    nhostSession: null as any,
    svelteSession: null as any,
    isAuth: false as boolean | undefined
  });

  const updateDebugInfo = async () => {
    if (typeof window === 'undefined') return;
    
    try {
      // localStorage
      debugInfo.localStorage = {...localStorage};
      
      // sessionStorage  
      debugInfo.sessionStorage = {...sessionStorage};
      
      // cookies
      debugInfo.cookies = document.cookie;
      
      // IndexedDB
      try {
        debugInfo.indexedDbDatabases = await indexedDB.databases();
      } catch (e) {
        debugInfo.indexedDbDatabases = ['Error accessing IndexedDB'];
      }
      
      // nHost session
      debugInfo.nhostSession = nhost.auth.getSession();
      
      // Svelte stores
      debugInfo.svelteSession = $session;
      debugInfo.isAuth = $isAuthenticated;
      
    } catch (error) {
      console.error('Debug info update error:', error);
    }
  };

  onMount(() => {
    updateDebugInfo();
    
    // Update every 2 seconds
    const interval = setInterval(updateDebugInfo, 2000);
    
    return () => clearInterval(interval);
  });

  const clearEverything = async () => {
    console.log('🚨 MANUAL CLEAR EVERYTHING');
    
    localStorage.clear();
    sessionStorage.clear();
    
    // Clear cookies
    document.cookie.split(";").forEach(cookie => {
      const eqPos = cookie.indexOf("=");
      const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;
      document.cookie = `${name.trim()}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
    });
    
    // Clear IndexedDB
    const databases = await indexedDB.databases();
    await Promise.all(
      databases.map(db => new Promise<void>((resolve) => {
        const deleteReq = indexedDB.deleteDatabase(db.name!);
        deleteReq.onsuccess = () => resolve();
        deleteReq.onerror = () => resolve();
      }))
    );
    
    // Force reload
    window.location.reload();
  };
</script>

<div class="fixed bottom-4 right-4 bg-red-500 text-white p-4 rounded-lg max-w-md max-h-96 overflow-auto text-xs z-50">
  <h3 class="font-bold text-sm mb-2">🚨 AUTH DEBUG INFO</h3>
  
  <button 
    onclick={clearEverything}
    class="bg-red-700 hover:bg-red-800 px-2 py-1 rounded text-xs mb-2"
  >
    CLEAR EVERYTHING
  </button>
  
  <div class="space-y-2">
    <div>
      <strong>nHost Session:</strong>
      <pre class="text-xs">{JSON.stringify(debugInfo.nhostSession?.user?.email || 'None', null, 1)}</pre>
    </div>
    
    <div>
      <strong>Svelte Session:</strong>
      <pre class="text-xs">{JSON.stringify(debugInfo.svelteSession?.user?.email || 'None', null, 1)}</pre>
    </div>
    
    <div>
      <strong>Is Authenticated:</strong>
      <span class={debugInfo.isAuth ? 'text-green-300' : 'text-red-300'}>
        {debugInfo.isAuth}
      </span>
    </div>
    
    <div>
      <strong>localStorage Keys:</strong>
      <pre class="text-xs">{JSON.stringify(Object.keys(debugInfo.localStorage), null, 1)}</pre>
    </div>
    
    <div>
      <strong>sessionStorage Keys:</strong>
      <pre class="text-xs">{JSON.stringify(Object.keys(debugInfo.sessionStorage), null, 1)}</pre>
    </div>
    
    <div>
      <strong>Cookies:</strong>
      <pre class="text-xs">{debugInfo.cookies || 'None'}</pre>
    </div>
    
    <div>
      <strong>IndexedDB:</strong>
      <pre class="text-xs">{JSON.stringify(debugInfo.indexedDbDatabases.map(db => typeof db === 'string' ? db : db.name), null, 1)}</pre>
    </div>
  </div>
</div>
