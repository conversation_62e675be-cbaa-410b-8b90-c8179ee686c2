<script lang="ts">
  import { session, isAuthenticated, userRole, userRoles } from '$lib/stores/auth';
  import { nhost } from '$lib/stores/nhost';

  function checkAuthState() {
    console.log('Auth Debug - Manual Check:', {
      session: $session,
      isAuthenticated: $isAuthenticated,
      userRole: $userRole,
      userRoles: $userRoles,
      nhostSession: nhost?.auth?.getSession?.()
    });
  }
</script>

<div class="fixed bottom-4 right-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg text-xs z-50">
  <h3 class="font-bold mb-2">Auth Debug</h3>
  <div class="space-y-1">
    <div>Session: {$session ? '✅' : '❌'}</div>
    <div>Authenticated: {$isAuthenticated ? '✅' : '❌'}</div>
    <div>User Role: {$userRole || 'none'}</div>
    <div>User Roles: {JSON.stringify($userRoles)}</div>
    <div>User ID: {$session?.user?.id || 'none'}</div>
  </div>
  <button 
    on:click={checkAuthState}
    class="mt-2 px-2 py-1 bg-blue-500 text-white rounded text-xs"
  >
    Check Console
  </button>
</div>
